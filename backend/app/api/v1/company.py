"""Company API endpoints."""

from typing import Any, Dict

from fastapi import Depends, HTTPException, Query, status

from app.api.base import BaseAPIRouter
from app.core.logging import get_logger
from app.dependencies.auth import get_current_user
from app.dependencies.org import get_org_context
from app.models.user import User
from app.services.company.interfaces import CompanyServiceInterface
from app.services.factory import get_company_service

logger = get_logger(__name__)

router = BaseAPIRouter(prefix="/companies", tags=["companies"])


@router.get("/enriched")
async def get_company_enriched(
    domain: str = Query(..., description="Company domain to fetch enrichment data for"),
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    company_service: CompanyServiceInterface = Depends(get_company_service),
) -> Dict[str, Any]:
    """
    Get enriched company data with all related information.

    This endpoint:
    1. Takes a domain query parameter
    2. Fetches company enrichment data from the database
    3. Returns structured company data including keywords, technologies, and department counts

    Args:
        domain: Company domain (e.g., "kubegrade.com")

    Returns:
        Dictionary containing company data with all enriched information
    """
    try:
        org_id, _ = org_context
        logger.info(f"Getting enriched company data for domain: {domain}")

        # Validate domain parameter
        if not domain or not domain.strip():
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Domain parameter is required and cannot be empty"
            )

        # Get company with all relations
        company_data = await company_service.get_company_with_all_relations(domain.strip().lower())

        if not company_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No enrichment data found for domain: {domain}"
            )

        logger.info(f"Found enriched data for company: {domain}")

        return company_data

    except HTTPException:
        # Re-raise HTTP exceptions as-is
        raise
    except Exception as e:
        logger.error(f"Error getting enriched company data for {domain}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while fetching company data"
        )
