"""
Pitch Processing Queue Handler

Handles background processing of uploaded pitch decks and one-pagers.
"""

from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from app.core.config import settings
from app.core.logging import get_logger
from app.models.deal import DealStatus
from app.models.deal_document import DealDocument, DocumentSource
from app.models.queue import Job
from app.services.factory import get_deal_service, get_file_service
from app.services.pdf_parser.service import PDFParserService
from app.services.pitch_parser.service import PitchParserService
from app.services.queue.handlers.base import BaseJobHandler
from app.utils.common import PyObjectId

logger = get_logger(__name__)


class PitchProcessingHandler(BaseJobHandler):
    """Handler for processing pitch upload jobs."""

    def __init__(self):
        super().__init__()
        self.pdf_parser: Optional[PDFParserService] = None
        self.pitch_parser: Optional[PitchParserService] = None
        self.file_service: Optional[Any] = None
        self.deal_service: Optional[Any] = None

    async def _initialize_services(self) -> None:
        """Initialize all required services."""
        try:
            # Initialize PDF and pitch parser services directly (they don't use factory)
            self.pdf_parser = PDFParserService()
            await self.pdf_parser.initialize()

            self.pitch_parser = PitchParserService()
            await self.pitch_parser.initialize()

            # Use factory pattern for services that need database
            from app.core.database import get_database

            db = await get_database()

            self.file_service = await get_file_service(db)
            self.deal_service = await get_deal_service(db)

            self.logger.info("Pitch processing handler initialized")
        except Exception as e:
            self.logger.error(
                f"Failed to initialize pitch processing handler: {str(e)}"
            )
            raise

    async def cleanup(self) -> None:
        """Cleanup all services."""
        try:
            if self.pdf_parser:
                await self.pdf_parser.cleanup()
            if self.pitch_parser:
                await self.pitch_parser.cleanup()
            if self.file_service:
                await self.file_service.cleanup()
            if self.deal_service:
                await self.deal_service.cleanup()

            self.logger.info("Pitch processing handler cleanup completed")
        except Exception as e:
            self.logger.error(
                f"Error during pitch processing handler cleanup: {str(e)}"
            )

    async def process(self, job: Job) -> Optional[Dict[str, Any]]:
        """Process a pitch upload job."""
        job_data = job.payload
        return await self.handle_job(job_data)

    async def handle_job(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle pitch processing job.

        Job data should contain:
        - temp_id: Temporary upload ID
        - org_id: Organization ID
        - user_id: User ID who uploaded
        - user_email: User email
        - deal_id: (Optional) Deal ID for existing deals
        """
        # Store job data for later use
        self._current_job_data = job_data

        # Extract and validate required job data
        temp_id = job_data.get("temp_id")
        org_id = job_data.get("org_id")
        user_id = job_data.get("user_id")
        user_email = job_data.get("user_email")
        deal_id = job_data.get("deal_id")  # Optional for existing deals

        # Validate required fields
        if not all([temp_id, org_id, user_id, user_email]):
            error_msg = f"Missing required job data: temp_id={temp_id}, org_id={org_id}, user_id={user_id}, user_email={user_email}"
            self.logger.error(error_msg)
            return {"success": False, "error": error_msg, "temp_id": temp_id}

        # Route to appropriate processing method based on whether deal_id is present
        if deal_id:
            self.logger.info(
                f"Processing pitch upload for existing deal: temp_id={temp_id}, deal_id={deal_id}, org_id={org_id}"
            )
            return await self._process_pitch_for_existing_deal(job_data)
        else:
            self.logger.info(
                f"Processing pitch upload for new deal: temp_id={temp_id}, org_id={org_id}"
            )
            return await self._process_pitch_for_new_deal(job_data)

    async def _process_pitch_for_new_deal(
        self, job_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process pitch upload for creating a new deal (existing logic)."""
        temp_id = job_data.get("temp_id")
        org_id = job_data.get("org_id")
        user_id = job_data.get("user_id")

        try:
            # Reconstruct S3 key from temp_id
            s3_key = f"deals/{org_id}/temp/{temp_id}/pitch_upload.pdf"

            # Ensure services are initialized
            if (
                not self.pdf_parser
                or not self.pitch_parser
                or not self.file_service
                or not self.deal_service
            ):
                await self._initialize_services()

            # Step 1: Extract text from PDF
            self.logger.info(f"Extracting text from PDF: {s3_key}")
            if not self.pdf_parser:
                raise RuntimeError("PDF parser service not initialized")

            pdf_data = await self.pdf_parser.extract_text_from_s3(
                s3_bucket=settings.S3_BUCKET_ASSETS, s3_key=s3_key
            )

            # Auto-detect pitch type based on content
            pitch_type = self.pdf_parser.detect_pitch_type(
                pdf_data["page_count"], pdf_data["text"]
            )

            self.logger.info(f"Auto-detected pitch type: {pitch_type}")

            # Step 2: Parse content with LLM
            self.logger.info(f"Parsing pitch content with LLM (type: {pitch_type})")
            if not self.pitch_parser:
                raise RuntimeError("Pitch parser service not initialized")

            parsed_data = await self.pitch_parser.parse_pitch_content(
                text_content=pdf_data["text"],
                pitch_type=pitch_type,
                pages=pdf_data["pages"],
                metadata=pdf_data.get("metadata"),
            )

            # Check if parsing failed due to timeout or other errors
            if parsed_data.get("parsing_metadata", {}).get("error"):
                self.logger.warning(
                    f"Pitch parsing had errors: {parsed_data['parsing_metadata']['error']}"
                )
                # Continue with partial data rather than failing completely

            # Log extraction results
            founders_found = parsed_data.get("founders", [])
            company_website = parsed_data.get("company_website")

            self.logger.info(
                f"Founder extraction completed: {len(founders_found)} founders found"
            )
            for i, founder in enumerate(founders_found):
                self.logger.info(
                    f"Founder {i + 1}: {founder.get('name', 'Unknown')} - {founder.get('role', [])}"
                )

            if company_website:
                self.logger.info(f"Company website extracted: {company_website}")
            else:
                self.logger.info("No company website found in pitch content")

            # Step 3: Generate short description
            self.logger.info("Generating short description")
            try:
                short_description = await self.pitch_parser.generate_short_description(
                    parsed_data=parsed_data,
                    text_content=pdf_data["text"],
                    pitch_type=pitch_type,
                )
            except Exception as e:
                self.logger.error(f"Failed to generate short description: {str(e)}")
                short_description = (
                    self.pitch_parser._create_fallback_short_description(
                        parsed_data, pitch_type
                    )
                )

            # Step 4: Generate executive summary
            self.logger.info("Generating executive summary")
            try:
                executive_summary_json = (
                    await self.pitch_parser.generate_executive_summary(
                        parsed_data=parsed_data,
                        text_content=pdf_data["text"],
                        pitch_type=pitch_type,
                    )
                )
            except Exception as e:
                self.logger.error(f"Failed to generate executive summary: {str(e)}")
                executive_summary_json = (
                    self.pitch_parser._create_fallback_executive_summary_json(
                        parsed_data, pitch_type
                    )
                )

            # Step 5: Generate markdown summary
            self.logger.info("Generating markdown summary")
            try:
                markdown_summary = await self.pitch_parser.generate_markdown_summary(
                    parsed_data=parsed_data,
                    text_content=pdf_data["text"],
                    pitch_type=pitch_type,
                )
            except Exception as e:
                self.logger.error(f"Failed to generate markdown summary: {str(e)}")
                markdown_summary = self.pitch_parser._create_fallback_markdown(
                    parsed_data, pitch_type
                )

            # Step 6: Create deal from parsed data
            self.logger.info("Creating deal from parsed data")
            deal = await self._create_deal_from_parsed_pitch(
                org_id=str(org_id),
                user_id=str(user_id),
                temp_id=str(temp_id),
                parsed_data=parsed_data,
                short_description=short_description,
                markdown_summary=markdown_summary,
                pitch_type=pitch_type,
                pdf_data=pdf_data,
                original_s3_key=s3_key,
            )

            # Step 7: Move PDF to permanent location
            self.logger.info(f"Moving PDF to permanent location for deal {deal.id}")
            final_s3_key = await self._move_pdf_to_permanent_location(
                temp_s3_key=s3_key, deal_id=str(deal.id), org_id=str(org_id)
            )

            # Step 8: Store executive summary
            self.logger.info(f"Storing executive summary for deal {deal.id}")
            executive_summary_s3_key = await self._store_executive_summary(
                deal_id=str(deal.id),
                org_id=str(org_id),
                executive_summary=executive_summary_json,
            )

            # Step 9: Update deal with final URLs
            await self._update_deal_with_final_urls(
                deal=deal,
                final_s3_key=final_s3_key,
                markdown_summary=markdown_summary,
                executive_summary_s3_key=executive_summary_s3_key,
                short_description=short_description,
            )

            self.logger.info(f"Successfully processed pitch upload: deal_id={deal.id}")

            return {
                "success": True,
                "deal_id": str(deal.id),
                "temp_id": temp_id,
                "pitch_type": pitch_type,
                "confidence_score": parsed_data.get("parsing_metadata", {}).get(
                    "confidence_score", 0.0
                ),
                "pages_processed": pdf_data["page_count"],
            }

        except Exception as e:
            self.logger.error(f"Error processing pitch upload {temp_id}: {str(e)}")

            # Try to create a minimal deal with error information
            try:
                await self._create_error_deal(
                    org_id=str(org_id),
                    user_id=str(user_id),
                    temp_id=str(temp_id),
                    error_message=str(e),
                    s3_key=s3_key,
                )
            except Exception as create_error:
                self.logger.error(f"Failed to create error deal: {str(create_error)}")

            return {"success": False, "error": str(e), "temp_id": temp_id}

    async def _process_pitch_for_existing_deal(
        self, job_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Process pitch upload for updating an existing deal."""
        temp_id = job_data.get("temp_id")
        deal_id = job_data.get("deal_id")
        org_id = job_data.get("org_id")

        try:
            # Reconstruct S3 key from temp_id
            s3_key = f"deals/{org_id}/temp/{temp_id}/pitch_upload.pdf"

            # Ensure services are initialized
            if (
                not self.pdf_parser
                or not self.pitch_parser
                or not self.file_service
                or not self.deal_service
            ):
                await self._initialize_services()

            # Get existing deal
            if not self.deal_service:
                raise RuntimeError("Deal service not initialized")
            existing_deal = await self.deal_service.get_deal(deal_id)
            if not existing_deal:
                raise RuntimeError(f"Deal {deal_id} not found")

            self.logger.info(
                f"Processing pitch for existing deal {deal_id}: {existing_deal.company_name}"
            )

            # Step 1: Extract text from PDF
            self.logger.info(f"Extracting text from PDF: {s3_key}")
            if not self.pdf_parser:
                raise RuntimeError("PDF parser service not initialized")

            pdf_data = await self.pdf_parser.extract_text_from_s3(
                s3_bucket=settings.S3_BUCKET_ASSETS, s3_key=s3_key
            )

            # Auto-detect pitch type based on content
            pitch_type = self.pdf_parser.detect_pitch_type(
                pdf_data["page_count"], pdf_data["text"]
            )

            self.logger.info(f"Auto-detected pitch type: {pitch_type}")

            # Step 2: Parse content with LLM
            self.logger.info(f"Parsing pitch content with LLM (type: {pitch_type})")
            if not self.pitch_parser:
                raise RuntimeError("Pitch parser service not initialized")

            parsed_data = await self.pitch_parser.parse_pitch_content(
                text_content=pdf_data["text"],
                pitch_type=pitch_type,
                pages=pdf_data["pages"],
                metadata=pdf_data.get("metadata"),
            )

            # Check if parsing failed due to timeout or other errors
            if parsed_data.get("parsing_metadata", {}).get("error"):
                self.logger.warning(
                    f"Pitch parsing had errors: {parsed_data['parsing_metadata']['error']}"
                )
                # Continue with partial data rather than failing completely

            # Step 3: Generate summaries
            self.logger.info("Generating summaries")
            try:
                short_description = await self.pitch_parser.generate_short_description(
                    parsed_data=parsed_data,
                    text_content=pdf_data["text"],
                    pitch_type=pitch_type,
                )
            except Exception as e:
                self.logger.error(f"Failed to generate short description: {str(e)}")
                short_description = (
                    self.pitch_parser._create_fallback_short_description(
                        parsed_data, pitch_type
                    )
                )

            try:
                executive_summary_json = (
                    await self.pitch_parser.generate_executive_summary(
                        parsed_data=parsed_data,
                        text_content=pdf_data["text"],
                        pitch_type=pitch_type,
                    )
                )
            except Exception as e:
                self.logger.error(f"Failed to generate executive summary: {str(e)}")
                executive_summary_json = (
                    self.pitch_parser._create_fallback_executive_summary_json(
                        parsed_data, pitch_type
                    )
                )

            try:
                markdown_summary = await self.pitch_parser.generate_markdown_summary(
                    parsed_data=parsed_data,
                    text_content=pdf_data["text"],
                    pitch_type=pitch_type,
                )
            except Exception as e:
                self.logger.error(f"Failed to generate markdown summary: {str(e)}")
                markdown_summary = self.pitch_parser._create_fallback_markdown(
                    parsed_data, pitch_type
                )

            # Step 4: Smart merge with existing deal data
            self.logger.info("Merging parsed data with existing deal")
            update_data = self._smart_merge_deal_data(
                existing_deal, parsed_data, short_description
            )

            # Step 5: Archive previous pitch deck if exists
            if existing_deal.pitch_deck_url:
                self.logger.info(
                    f"Archiving previous pitch deck: {existing_deal.pitch_deck_url}"
                )
                await self._archive_previous_pitch_deck(
                    existing_deal.pitch_deck_url, str(deal_id)
                )

            # Step 6: Move PDF to permanent location
            self.logger.info(f"Moving PDF to permanent location for deal {deal_id}")
            final_s3_key = await self._move_pdf_to_permanent_location(
                temp_s3_key=s3_key, deal_id=str(deal_id), org_id=str(org_id)
            )

            # Step 7: Store executive summary
            self.logger.info(f"Storing executive summary for deal {deal_id}")
            executive_summary_s3_key = await self._store_executive_summary(
                deal_id=str(deal_id),
                org_id=str(org_id),
                executive_summary=executive_summary_json,
            )

            # Step 8: Update deal with final URLs and merged data
            await self._update_existing_deal_with_final_urls(
                deal_id=str(deal_id),
                final_s3_key=final_s3_key,
                markdown_summary=markdown_summary,
                executive_summary_s3_key=executive_summary_s3_key,
                update_data=update_data,
                pitch_type=pitch_type,
                pdf_data=pdf_data,
                parsed_data=parsed_data,
            )

            self.logger.info(
                f"Successfully processed pitch upload for existing deal: deal_id={deal_id}"
            )

            return {
                "success": True,
                "deal_id": str(deal_id),
                "temp_id": temp_id,
                "pitch_type": pitch_type,
                "confidence_score": parsed_data.get("parsing_metadata", {}).get(
                    "confidence_score", 0.0
                ),
                "pages_processed": pdf_data["page_count"],
                "updated_fields": list(update_data.keys()),
            }

        except Exception as e:
            self.logger.error(
                f"Error processing pitch upload for existing deal {deal_id}: {str(e)}"
            )
            return {
                "success": False,
                "error": str(e),
                "temp_id": temp_id,
                "deal_id": deal_id,
            }

    async def _create_deal_from_parsed_pitch(
        self,
        org_id: str,
        user_id: str,
        temp_id: str,
        parsed_data: Dict[str, Any],
        short_description: str,
        markdown_summary: str,
        pitch_type: str,
        pdf_data: Dict[str, Any],
        original_s3_key: str,
    ) -> Any:
        """Create a deal from parsed pitch data."""
        if not self.deal_service:
            raise RuntimeError("Deal service not initialized")

        # Extract and process founder information
        founders = self._extract_founders_from_parsed_data(parsed_data)

        # Prepare deal data
        deal_data = {
            "company_name": parsed_data.get("company_name")
            or f"Company from {pitch_type}",
            "company_website": parsed_data.get(
                "company_website"
            ),  # Add extracted company website
            "short_description": short_description,  # Use the generated short description
            "sector": parsed_data.get("sector", []),
            "status": DealStatus.TRIAGE,
            "founders": founders,  # Add extracted founders
            "enriched_data": {
                "pitch_parsing": parsed_data,
                "pdf_metadata": pdf_data.get("metadata", {}),
                "processing_metadata": {
                    "temp_id": temp_id,
                    "pitch_type": pitch_type,
                    "processed_at": datetime.now(timezone.utc).isoformat(),
                    "original_filename": pdf_data.get("filename"),
                    "page_count": pdf_data.get("page_count"),
                    "founders_extracted": len(founders),
                },
            },
            "tags": [pitch_type] if pitch_type == "one_pager" else [],
            "timeline": [
                {
                    "event": "Deal created from pitch upload",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "details": {
                        "pitch_type": pitch_type,
                        "confidence_score": parsed_data.get("parsing_metadata", {}).get(
                            "confidence_score"
                        ),
                        "pages": pdf_data.get("page_count"),
                        "founders_found": len(founders),
                    },
                }
            ],
        }

        # Create the deal
        deal = await self.deal_service.create_deal_from_pitch(
            org_id=org_id, created_by=user_id, deal_data=deal_data
        )

        return deal

    def _extract_founders_from_parsed_data(
        self, parsed_data: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Extract and validate founder information from parsed pitch data."""
        founders = []
        raw_founders = parsed_data.get("founders", [])

        if not raw_founders or not isinstance(raw_founders, list):
            self.logger.info("No founders found in parsed data")
            return founders

        for i, raw_founder in enumerate(raw_founders):
            if not isinstance(raw_founder, dict):
                self.logger.warning(f"Invalid founder data at index {i}: {raw_founder}")
                continue

            # Extract and validate founder data
            founder = {
                "name": raw_founder.get("name"),
                "role": raw_founder.get("role", []),
                "linkedin": raw_founder.get("linkedin"),
                "email": raw_founder.get("email"),
                "serial_founder": raw_founder.get("serial_founder", False),
                "profile_picture": raw_founder.get("profile_picture"),
                "experience": raw_founder.get("experience", []),
                "skills": raw_founder.get("skills", []),
                "education": raw_founder.get("education", []),
                "achievements": raw_founder.get("achievements", []),
            }

            # Validate required fields
            if not founder["name"]:
                self.logger.warning(f"Founder at index {i} missing name, skipping")
                continue

            # Ensure role is a list
            if isinstance(founder["role"], str):
                founder["role"] = [founder["role"]]
            elif not isinstance(founder["role"], list):
                founder["role"] = []

            # Ensure other list fields are properly formatted
            for field in ["experience", "skills", "education", "achievements"]:
                if not isinstance(founder[field], list):
                    founder[field] = []

            founders.append(founder)
            self.logger.info(
                f"Extracted founder: {founder['name']} - {founder['role']}"
            )

        self.logger.info(
            f"Successfully extracted {len(founders)} founders from pitch data"
        )
        return founders

    async def _move_pdf_to_permanent_location(
        self, temp_s3_key: str, deal_id: str, org_id: str
    ) -> str:
        """Move PDF from temp location to permanent deal location."""
        if not self.file_service:
            raise RuntimeError("File service not initialized")

        # Generate final S3 key - use deals/ instead of decks/

        final_s3_key = f"deals/{deal_id}/pitch_deck.pdf"

        # Copy file to new location
        await self.file_service.copy_s3_object(
            source_bucket=settings.S3_BUCKET_ASSETS,
            source_key=temp_s3_key,
            dest_bucket=settings.S3_BUCKET_ASSETS,
            dest_key=final_s3_key,
        )

        # Delete temp file
        await self.file_service.delete_s3_object(
            bucket=settings.S3_BUCKET_ASSETS, key=temp_s3_key
        )

        return final_s3_key

    async def _store_executive_summary(
        self, deal_id: str, org_id: str, executive_summary: Any
    ) -> str:
        """Store executive summary in deals/ structure."""
        if not self.file_service:
            raise RuntimeError("File service not initialized")

        # Generate S3 key for executive summary
        executive_summary_s3_key = f"deals/{deal_id}/research/executive_summary.json"

        # Convert JSON to string and upload to S3
        import json

        executive_summary_json_str = json.dumps(executive_summary, indent=2)

        await self.file_service.upload_text_to_s3(
            bucket=settings.S3_BUCKET_ASSETS,
            key=executive_summary_s3_key,
            content=executive_summary_json_str,
            content_type="application/json",
        )

        return executive_summary_s3_key

    async def _update_deal_with_final_urls(
        self,
        deal: Any,
        final_s3_key: str,
        markdown_summary: str,
        executive_summary_s3_key: str,
        short_description: str,
    ) -> None:
        """Update deal with final URLs for PDF and markdown."""
        if not self.file_service or not self.deal_service:
            raise RuntimeError("File service or deal service not initialized")

        # Generate public URLs
        pitch_deck_url = f"https://{settings.S3_BUCKET_ASSETS}.s3.{settings.AWS_REGION}.amazonaws.com/{final_s3_key}"

        # Save markdown to S3 - use deals/ structure
        markdown_s3_key = f"deals/{deal.org_id}/{deal.id}/research/pitch_summary.md"
        await self.file_service.upload_text_to_s3(
            bucket=settings.S3_BUCKET_ASSETS,
            key=markdown_s3_key,
            content=markdown_summary,
            content_type="text/markdown",
        )

        context_block_url = f"https://{settings.S3_BUCKET_ASSETS}.s3.{settings.AWS_REGION}.amazonaws.com/{markdown_s3_key}"

        # Generate executive summary URL
        executive_summary_url = f"https://{settings.S3_BUCKET_ASSETS}.s3.{settings.AWS_REGION}.amazonaws.com/{executive_summary_s3_key}"

        # Update deal
        await self.deal_service.update_deal(
            deal_id=str(deal.id),
            update_data={
                "pitch_deck_url": pitch_deck_url,
                "context_block_url": context_block_url,
                "executive_summary_url": executive_summary_url,
                "executive_summary_json_url": executive_summary_url,  # Store JSON URL in new field
                "description": short_description,  # Update with generated short description
            },
        )

        # Create deal document for the pitch deck
        await self._create_pitch_deck_document(
            deal_id=str(deal.id),
            org_id=str(deal.org_id),
            user_id=str(deal.created_by),
            user_email="<EMAIL>",  # Default for system-generated documents
            final_s3_key=final_s3_key,
            pitch_type="pitch_deck",  # Default type for new deals
            pdf_data={"page_count": 0, "filename": "pitch_deck.pdf"},  # Default data
        )

    async def _create_error_deal(
        self, org_id: str, user_id: str, temp_id: str, error_message: str, s3_key: str
    ) -> None:
        """Create a minimal deal when processing fails."""
        if not self.deal_service:
            raise RuntimeError("Deal service not initialized")

        deal_data = {
            "company_name": f"Failed Upload {temp_id[:8]}",
            "description": "Pitch processing failed",
            "status": DealStatus.TRIAGE,
            "enriched_data": {
                "processing_error": {
                    "error": error_message,
                    "temp_id": temp_id,
                    "s3_key": s3_key,
                    "failed_at": datetime.now(timezone.utc).isoformat(),
                }
            },
            "tags": ["processing_failed"],
            "timeline": [
                {
                    "event": "Deal creation failed during pitch processing",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "details": {"error": error_message},
                }
            ],
        }

        await self.deal_service.create_deal_from_pitch(
            org_id=org_id, created_by=user_id, deal_data=deal_data
        )

    def _smart_merge_deal_data(
        self, existing_deal: Any, parsed_data: Dict[str, Any], short_description: str
    ) -> Dict[str, Any]:
        """Smart merge parsed pitch data with existing deal data."""
        update_data = {}

        # Core fields: Only update if missing or significantly different
        if parsed_data.get("company_name"):
            if not existing_deal.company_name:
                update_data["company_name"] = parsed_data["company_name"]
                self.logger.info(
                    f"Updated company_name: {existing_deal.company_name} -> {parsed_data['company_name']}"
                )

        if parsed_data.get("company_website"):
            if not existing_deal.company_website:
                update_data["company_website"] = parsed_data["company_website"]
                self.logger.info(
                    f"Updated company_website: {existing_deal.company_website} -> {parsed_data['company_website']}"
                )

        if short_description:
            update_data["short_description"] = short_description
            self.logger.info(
                f"Updated short_description: {existing_deal.short_description} -> {short_description}"
            )

        # Sector: Only update if different
        if parsed_data.get("sector"):
            existing_sector = existing_deal.sector or []
            new_sector = parsed_data["sector"]
            if set(existing_sector) != set(new_sector):
                update_data["sector"] = new_sector
                self.logger.info(f"Updated sector: {existing_sector} -> {new_sector}")

        # Stage: Only update if different
        if parsed_data.get("stage"):
            if not existing_deal.stage or existing_deal.stage != parsed_data["stage"]:
                update_data["stage"] = parsed_data["stage"]
                self.logger.info(
                    f"Updated stage: {existing_deal.stage} -> {parsed_data['stage']}"
                )

        # Founders: Merge intelligently (avoid duplicates)
        # if parsed_data.get("founders"):
        #     existing_founders = existing_deal.founders or []
        #     merged_founders = self._merge_founders(
        #         existing_founders, parsed_data["founders"]
        #     )
        #     if (
        #         len(existing_founders) != len(merged_founders)
        #         or existing_founders != merged_founders
        #     ):
        #         update_data["founders"] = merged_founders
        #         self.logger.info(
        #             f"Merged founders: {len(existing_founders)} -> {len(merged_founders)} total founders"
        #         )

        # Enriched data: Preserve existing structure and append new pitch parsing
        enriched_data = (
            existing_deal.enriched_data.copy() if existing_deal.enriched_data else {}
        )

        # Add new pitch parsing data
        enriched_data["pitch_parsing"] = parsed_data
        enriched_data["pitch_processing_metadata"] = {
            "processed_at": datetime.now(timezone.utc).isoformat(),
            "pitch_type": parsed_data.get("pitch_type", "unknown"),
            "confidence_score": parsed_data.get("parsing_metadata", {}).get(
                "confidence_score", 0.0
            ),
            "pages_processed": parsed_data.get("page_count", 0),
            "founders_extracted": len(parsed_data.get("founders", [])),
        }

        update_data["enriched_data"] = enriched_data
        self.logger.info("Updated enriched_data with new pitch parsing information")

        return update_data

    def _merge_founders(
        self, existing_founders: List[Any], new_founders: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Merge new founders with existing founders, avoiding duplicates."""
        if not existing_founders:
            return new_founders

        if not new_founders:
            return existing_founders

        # Convert existing founders to dict format for easier comparison
        existing_founders_dict = []
        for founder in existing_founders:
            if hasattr(founder, "model_dump"):
                existing_founders_dict.append(founder.model_dump())
            else:
                existing_founders_dict.append(founder)

        # Create a set of existing founder names for quick lookup
        existing_names = {
            founder.get("name", "").lower().strip()
            for founder in existing_founders_dict
            if founder.get("name")
        }

        # Add new founders that don't already exist
        merged_founders = existing_founders_dict.copy()
        for new_founder in new_founders:
            new_name = new_founder.get("name", "").lower().strip()
            if new_name and new_name not in existing_names:
                merged_founders.append(new_founder)
                existing_names.add(new_name)
                self.logger.info(f"Added new founder: {new_founder.get('name')}")

        return merged_founders

    async def _archive_previous_pitch_deck(
        self, previous_url: str, deal_id: str
    ) -> None:
        """Archive the previous pitch deck to preserve history."""
        try:
            if not self.file_service:
                raise RuntimeError("File service not initialized")

            # Extract S3 key from URL
            if "s3.amazonaws.com" in previous_url:
                s3_key = previous_url.split(".com/")[-1]
            else:
                s3_key = previous_url

            # Generate archive key
            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
            archive_key = f"deals/{deal_id}/archive/pitch_deck_{timestamp}.pdf"

            # Copy to archive location
            await self.file_service.copy_s3_object(
                source_bucket=settings.S3_BUCKET_ASSETS,
                source_key=s3_key,
                dest_bucket=settings.S3_BUCKET_ASSETS,
                dest_key=archive_key,
            )

            self.logger.info(f"Archived previous pitch deck to: {archive_key}")

        except Exception as e:
            self.logger.warning(f"Failed to archive previous pitch deck: {str(e)}")

    async def _create_pitch_deck_document(
        self,
        deal_id: str,
        org_id: str,
        user_id: str,
        user_email: str,
        final_s3_key: str,
        pitch_type: str,
        pdf_data: Dict[str, Any],
    ) -> Optional[DealDocument]:
        """Create a deal document for the pitch deck."""
        try:
            # Get file metadata from S3 using head_object
            if not self.file_service:
                raise RuntimeError("File service not initialized")

            # Get file info from S3 using head_object
            try:
                file_info = self.file_service.s3_client.head_object(
                    Bucket=settings.S3_BUCKET_ASSETS, Key=final_s3_key
                )
                file_size = file_info.get("ContentLength", 0)
                mime_type = file_info.get("ContentType", "application/pdf")
            except Exception as e:
                self.logger.warning(
                    f"Could not get S3 file info for {final_s3_key}: {str(e)}"
                )
                # Use fallback values
                file_size = pdf_data.get("file_size", 0)
                mime_type = "application/pdf"

            filename = f"pitch_deck_{pitch_type}.pdf"

            # Determine document type
            document_type = DealDocument.determine_document_type(filename, mime_type)

            # Create deal document for pitch deck
            deal_document = DealDocument(
                deal_id=PyObjectId(deal_id),
                org_id=PyObjectId(org_id),
                source=DocumentSource.PITCH_DECK,
                submission_id=None,  # Not from a submission
                submission_file_id=None,  # Not from a submission file
                uploaded_by_user_id=PyObjectId(user_id),
                uploaded_by_email=user_email,
                uploaded_by_name="Pitch Processing",
                uploaded_by_role="system",
                filename=filename,
                s3_key=final_s3_key,
                s3_bucket=settings.S3_BUCKET_ASSETS,
                mime_type=mime_type,
                file_size=file_size,
                document_type=document_type,
                tags=["pitch-deck", pitch_type, "auto-generated"],
                metadata={
                    "pitch_type": pitch_type,
                    "page_count": pdf_data.get("page_count", 0),
                    "processing_timestamp": datetime.now(timezone.utc).isoformat(),
                    "original_filename": pdf_data.get("filename", "unknown"),
                },
            )

            # Mark as ready
            deal_document.mark_ready()

            # Generate URLs
            deal_document.download_url = await self._generate_download_url(
                deal_document
            )
            deal_document.preview_url = await self._generate_preview_url(deal_document)

            # Save to database
            deal_document = await deal_document.save()

            self.logger.info(
                f"Created pitch deck document: {deal_document.id} for deal {deal_id}"
            )
            return deal_document

        except Exception as e:
            self.logger.error(
                f"Failed to create pitch deck document for deal {deal_id}: {str(e)}"
            )
            return None

    async def _generate_download_url(self, document: DealDocument) -> str:
        """Generate a pre-signed download URL for the document."""
        try:
            if not self.file_service:
                raise RuntimeError("File service not initialized")

            # Generate pre-signed URL for download using the file service's S3 client
            download_url = self.file_service.s3_client.generate_presigned_url(
                "get_object",
                Params={
                    "Bucket": document.s3_bucket,
                    "Key": document.s3_key,
                    "ResponseContentDisposition": f'attachment; filename="{document.filename}"',
                },
                ExpiresIn=3600,  # 1 hour
            )

            return download_url

        except Exception as e:
            self.logger.error(
                f"Failed to generate download URL for document {document.id}: {str(e)}"
            )
            return ""

    async def _generate_preview_url(self, document: DealDocument) -> str:
        """Generate a preview URL for the document."""
        try:
            # For now, return the same as download URL
            # In the future, this could be a different URL for preview
            return await self._generate_download_url(document)

        except Exception as e:
            self.logger.error(
                f"Failed to generate preview URL for document {document.id}: {str(e)}"
            )
            return ""

    async def _update_existing_deal_with_final_urls(
        self,
        deal_id: str,
        final_s3_key: str,
        markdown_summary: str,
        executive_summary_s3_key: str,
        update_data: Dict[str, Any],
        pitch_type: str,
        pdf_data: Dict[str, Any],
        parsed_data: Dict[str, Any],
    ) -> None:
        """Update existing deal with final URLs and merged data."""
        if not self.file_service or not self.deal_service:
            raise RuntimeError("File service or deal service not initialized")

        # Generate public URLs
        pitch_deck_url = f"https://{settings.S3_BUCKET_ASSETS}.s3.{settings.AWS_REGION}.amazonaws.com/{final_s3_key}"

        # Save markdown to S3
        markdown_s3_key = f"deals/{deal_id}/research/pitch_summary.md"
        await self.file_service.upload_text_to_s3(
            bucket=settings.S3_BUCKET_ASSETS,
            key=markdown_s3_key,
            content=markdown_summary,
            content_type="text/markdown",
        )

        context_block_url = f"https://{settings.S3_BUCKET_ASSETS}.s3.{settings.AWS_REGION}.amazonaws.com/{markdown_s3_key}"

        # Generate executive summary URL
        executive_summary_url = f"https://{settings.S3_BUCKET_ASSETS}.s3.{settings.AWS_REGION}.amazonaws.com/{executive_summary_s3_key}"

        # Add URLs to update data
        update_data.update({
            "pitch_deck_url": pitch_deck_url,
            "context_block_url": context_block_url,
            "executive_summary_url": executive_summary_url,
            "executive_summary_json_url": executive_summary_url,  # Store JSON URL in new field
        })

        # Add timeline event
        timeline_event = {
            "event": "Pitch deck uploaded and processed",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "details": {
                "pitch_type": pitch_type,
                "confidence_score": parsed_data.get("parsing_metadata", {}).get(
                    "confidence_score", 0.0
                ),
                "pages_processed": pdf_data.get("page_count", 0),
                "founders_extracted": len(parsed_data.get("founders", [])),
                "updated_fields": list(update_data.keys()),
            },
        }

        # Log what we're about to update
        self.logger.info(f"Updating deal {deal_id} with data: {update_data}")

        # Update deal with all changes
        updated_deal = await self.deal_service.update_deal(
            deal_id=deal_id,
            update_data=update_data,
        )

        if updated_deal:
            self.logger.info(f"Successfully updated deal {deal_id}")
            # Log the updated deal's key fields
            self.logger.info(f"Updated deal company_name: {updated_deal.company_name}")
            self.logger.info(
                f"Updated deal pitch_deck_url: {updated_deal.pitch_deck_url}"
            )
            self.logger.info(
                f"Updated deal executive_summary_url: {updated_deal.executive_summary_url}"
            )
            self.logger.info(
                f"Updated deal enriched_data keys: {list(updated_deal.enriched_data.keys()) if updated_deal.enriched_data else 'None'}"
            )

            # Refresh the deal to ensure we have the latest data
            refreshed_deal = await self.deal_service.get_deal(deal_id)
            if refreshed_deal:
                self.logger.info(
                    f"Refreshed deal company_name: {refreshed_deal.company_name}"
                )
                self.logger.info(
                    f"Refreshed deal pitch_deck_url: {refreshed_deal.pitch_deck_url}"
                )
                self.logger.info(
                    f"Refreshed deal executive_summary_url: {refreshed_deal.executive_summary_url}"
                )
                self.logger.info(
                    f"Refreshed deal enriched_data keys: {list(refreshed_deal.enriched_data.keys()) if refreshed_deal.enriched_data else 'None'}"
                )
        else:
            self.logger.error(f"Failed to update deal {deal_id}")

        # Add timeline event separately
        await self.deal_service.add_timeline_event(
            deal_id=deal_id,
            event=timeline_event["event"],
            notes=f"Pitch deck processed: {pitch_type}, {pdf_data.get('page_count', 0)} pages, {len(parsed_data.get('founders', []))} founders extracted",
        )

        # Create deal document for the pitch deck
        job_data = getattr(self, "_current_job_data", {})
        user_id = job_data.get("user_id", "system")
        user_email = job_data.get("user_email", "<EMAIL>")

        await self._create_pitch_deck_document(
            deal_id=deal_id,
            org_id=job_data.get("org_id", ""),
            user_id=user_id,
            user_email=user_email,
            final_s3_key=final_s3_key,
            pitch_type=pitch_type,
            pdf_data=pdf_data,
        )

        self.logger.info(
            f"Updated existing deal {deal_id} with pitch processing results"
        )
