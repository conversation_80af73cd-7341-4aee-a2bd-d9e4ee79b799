"""Founder service implementation."""

from typing import Any, Dict, List, Optional

from app.core.config import settings
from app.core.logging import get_logger
from app.services.base import BaseService
from app.storage.rds_storage import RDSStorage

logger = get_logger(__name__)


class FounderService(BaseService):
    """Service for managing founder data and operations."""

    def __init__(self):
        super().__init__()
        self.rds = RDSStorage()

    async def initialize(self) -> None:
        """Initialize the founder service."""
        try:
            await self.rds.initialize()
            logger.info("Founder service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize founder service: {e}")
            raise

    async def cleanup(self) -> None:
        """Cleanup founder service resources."""
        try:
            await self.rds.cleanup()
            logger.info("Founder service cleaned up successfully")
        except Exception as e:
            logger.error(f"Error during founder service cleanup: {e}")

    async def get_founder_with_all_relations(self, founder_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a single founder with all related data.
        
        Args:
            founder_id: The founder UUID
            
        Returns:
            Dictionary with founder data and all relations, or None if not found
        """
        try:
            logger.info(f"Getting founder with relations: {founder_id}")
            result = await self.rds.get_founder_with_all_relations(founder_id)

            if result:
                # Convert to camelCase for frontend compatibility
                return self._convert_to_camel_case(result)

            return None

        except Exception as e:
            logger.error(f"Error getting founder with relations {founder_id}: {e}")
            await self.handle_error(e, {"founder_id": founder_id})
            return None

    async def get_founders_by_company_id(self, company_id: str) -> List[Dict[str, Any]]:
        """
        Get all founders for a company with their related data.
        
        Args:
            company_id: The company identifier (website URL)
            
        Returns:
            List of founder dictionaries with all relations
        """
        try:
            logger.info(f"Getting founders for company: {company_id}")
            results = await self.rds.get_founders_by_company_id(company_id)
            
            # Convert to camelCase for frontend compatibility
            return [self._convert_to_camel_case(result) for result in results]
            
        except Exception as e:
            logger.error(f"Error getting founders for company {company_id}: {e}")
            await self.handle_error(e, {"company_id": company_id})
            return []

    def _convert_to_camel_case(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convert snake_case keys to camelCase for frontend compatibility.
        
        Args:
            data: Dictionary with snake_case keys
            
        Returns:
            Dictionary with camelCase keys
        """
        def to_camel_case(snake_str: str) -> str:
            components = snake_str.split('_')
            return components[0] + ''.join(word.capitalize() for word in components[1:])
        
        def convert_dict(obj: Any) -> Any:
            if isinstance(obj, dict):
                return {to_camel_case(k): convert_dict(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_dict(item) for item in obj]
            else:
                return obj
        
        return convert_dict(data)
