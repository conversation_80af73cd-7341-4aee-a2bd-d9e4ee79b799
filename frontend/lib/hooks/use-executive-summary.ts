import { useState, useEffect, useCallback } from 'react'
import { DealDetailAPI } from '@/lib/api/deal-detail-api'
import { useToast } from '@/components/ui/use-toast'

interface ExecutiveSummaryData {
  executive_summary: { title: string; content: string }[]
  metadata?: {
    model_used: string
    generated_at: string
  }
}

interface UseExecutiveSummaryReturn {
  data: ExecutiveSummaryData | null
  loading: boolean
  error: string | null
  refreshing: boolean
  hasData: boolean
  refresh: () => Promise<void>
  reload: () => Promise<void>
}

export function useExecutiveSummary({ dealId }: { dealId: string }): UseExecutiveSummaryReturn {
  const [data, setData] = useState<ExecutiveSummaryData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [refreshing, setRefreshing] = useState(false)
  const { toast } = useToast()

  const fetchExecutiveSummary = useCallback(async () => {
    try {
      setError(null)
      const executiveSummaryData = await DealDetailAPI.getExecutiveSummary(dealId)
      setData(executiveSummaryData)
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to fetch executive summary'
      setError(errorMessage)
      console.error('Error fetching executive summary:', err)
    }
  }, [dealId])

  const load = useCallback(async () => {
    setLoading(true)
    await fetchExecutiveSummary()
    setLoading(false)
  }, [fetchExecutiveSummary])

  const refresh = useCallback(async () => {
    setRefreshing(true)
    await fetchExecutiveSummary()
    setRefreshing(false)
    
    toast({
      title: "Executive Summary Refreshed",
      description: "The executive summary has been updated.",
    })
  }, [fetchExecutiveSummary, toast])

  const reload = useCallback(async () => {
    setError(null)
    await load()
  }, [load])

  useEffect(() => {
    load()
  }, [load])

  const hasData = Boolean(data?.executive_summary?.length)

  return {
    data,
    loading,
    error,
    refreshing,
    hasData,
    refresh,
    reload
  }
} 