"use client"

import { useState, useRef, useEffect } from "react"
import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Upload, 
  FileText, 
  Download, 
  Eye, 
  RefreshCw,
  CheckCircle,
  AlertCircle,
  X
} from "lucide-react"
import { cn } from "@/lib/utils"
import { DealDetailData } from "@/lib/types/deal-detail"
import { useToast } from "@/components/ui/use-toast"
import { usePitchUploader } from "@/hooks/use-pitch-uploader"

interface PitchDeckUploadProps {
  deal: DealDetailData
  onPitchUploaded?: () => void
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  })
}

export function PitchDeckUpload({ deal, onPitchUploaded }: PitchDeckUploadProps) {
  const [showUpload, setShowUpload] = useState(false)
  const [busy, setBusy] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { toast } = useToast()
  
  const {
    uploadState,
    uploadPitch,
    resetUpload,
    isUploading
  } = usePitchUploader(deal.id)

  // Check if deal has a pitch deck
  const hasPitchDeck = deal.pitch_deck_url || 
    (deal.enriched_data?.processing_metadata?.pitch_type) ||
    (deal.documents?.some(doc => doc.source === 'pitch_deck'))

  // Get pitch deck document if it exists
  const pitchDeckDocument = deal.documents?.find(doc => doc.source === 'pitch_deck')

  // Handle successful upload
  useEffect(() => {
    if (uploadState.status === 'completed') {
      toast({
        title: "Pitch deck uploaded successfully",
        description: "Processing has been queued. Your pitch deck will be analyzed and enriched within 2-3 minutes. You'll receive a notification when processing is complete.",
      })
      
      // Reset upload state after a longer delay to let user read the message
      setTimeout(() => {
        resetUpload()
        setShowUpload(false)
        onPitchUploaded?.()
      }, 4000)
    }
  }, [uploadState.status, resetUpload, onPitchUploaded, toast])

  // Handle upload errors
  useEffect(() => {
    if (uploadState.status === 'error' && uploadState.error) {
      toast({
        title: "Upload failed",
        description: uploadState.error,
        variant: "destructive"
      })
    }
  }, [uploadState.status, uploadState.error, toast])

  // Guard to prevent double invocation
  const uploadInProgress = busy || isUploading || uploadState.status === 'preparing' || uploadState.status === 'uploading' || uploadState.status === 'processing'

  const handleUploadClick = () => {
    if (!uploadInProgress) fileInputRef.current?.click()
  }

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    console.log('[PitchDeckUpload] handleFileSelect called', { uploadInProgress, event })
    
    if (uploadInProgress) {
      console.log('[PitchDeckUpload] Upload in progress, ignoring file select')
      return
    }
    
    const files = event.target.files
    console.log('[PitchDeckUpload] Files from event:', files)
    
    if (!files || files.length === 0) {
      console.log('[PitchDeckUpload] No files selected')
      return
    }
    
    const file = files[0]
    console.log('[PitchDeckUpload] Selected file:', { name: file.name, type: file.type, size: file.size })
    
    setBusy(true)
    try {
      console.log('[PitchDeckUpload] Starting upload...')
      await uploadPitch(file)
      console.log('[PitchDeckUpload] Upload completed successfully')
    } catch (error) {
      console.error('[PitchDeckUpload] Upload failed:', error)
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : "Unknown error occurred",
        variant: "destructive"
      })
    } finally {
      console.log('[PitchDeckUpload] Resetting busy state and file input')
      setBusy(false)
      // Reset file input only after upload attempt
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handlePreview = () => {
    if (deal.pitch_deck_url) {
      window.open(deal.pitch_deck_url, '_blank', 'noopener,noreferrer')
    } else if (pitchDeckDocument?.preview_url) {
      window.open(pitchDeckDocument.preview_url, '_blank', 'noopener,noreferrer')
    }
  }

  const handleDownload = () => {
    if (deal.pitch_deck_url) {
      window.open(deal.pitch_deck_url, '_blank', 'noopener,noreferrer')
    } else if (pitchDeckDocument?.download_url) {
      window.open(pitchDeckDocument.download_url, '_blank', 'noopener,noreferrer')
    }
  }

  const handleReplace = () => {
    setShowUpload(true)
    resetUpload()
  }

  const handleCancelUpload = () => {
    setShowUpload(false)
    resetUpload()
  }

  // If uploading, show upload progress
  if (isUploading || uploadState.status === 'completed') {
    return (
      <Card className="border-2 border-dashed border-primary/20 bg-primary/5">
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              {uploadState.status === 'completed' ? (
                <CheckCircle className="size-5 text-green-600" />
              ) : (
                <RefreshCw className={cn(
                  "size-5 text-primary",
                  isUploading && "animate-spin"
                )} />
              )}
              <div className="flex-1">
                <h3 className="text-sm font-semibold">
                  {uploadState.status === 'completed' 
                    ? 'Pitch deck uploaded successfully!' 
                    : 'Uploading pitch deck...'
                  }
                </h3>
                <p className="text-xs text-muted-foreground">
                  {uploadState.status === 'preparing' && 'Preparing upload...'}
                  {uploadState.status === 'uploading' && 'Uploading to server...'}
                  {uploadState.status === 'processing' && 'Processing pitch deck...'}
                  {uploadState.status === 'completed' && 'Processing queued - analysis will complete in 2-3 minutes'}
                </p>
              </div>
              {uploadState.status === 'completed' && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCancelUpload}
                  className="size-8 p-0"
                >
                  <X className="size-4" />
                </Button>
              )}
            </div>
            
            {isUploading && (
              <div className="space-y-2">
                <Progress value={uploadState.progress} className="h-2" />
                <p className="text-xs text-muted-foreground">
                  {Math.round(uploadState.progress)}% complete
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  // If showing upload interface
  if (showUpload) {
    return (
      <Card className="border-2 border-dashed border-primary/20 bg-primary/5">
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold">Upload Pitch Deck</h3>
                <p className="text-sm text-muted-foreground">
                  This is the main deck used for parsing, enrichment, and scoring.
                </p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCancelUpload}
                className="size-8 p-0"
              >
                <X className="size-4" />
              </Button>
            </div>
            <div
              className="space-y-4 text-center border-2 border-dashed rounded-lg p-6 bg-white/50 hover:bg-primary/10 transition-colors cursor-pointer"
              onDragOver={e => { e.preventDefault(); e.stopPropagation(); }}
              onDrop={e => {
                e.preventDefault();
                e.stopPropagation();
                if (uploadInProgress) return;
                const files = e.dataTransfer.files;
                if (files && files.length > 0) {
                  console.log('[PitchDeckUpload] Files dropped:', files)
                  handleFileSelect({ target: { files } } as any)
                }
              }}
              style={{ minHeight: 120, opacity: uploadInProgress ? 0.6 : 1, pointerEvents: uploadInProgress ? 'none' : 'auto' }}
            >
              <div className="flex justify-center">
                <div className="flex size-16 items-center justify-center rounded-full bg-primary/10">
                  <Upload className="size-8 text-primary" />
                </div>
              </div>
              <div>
                <p className="mb-4 text-sm text-muted-foreground">
                  Drag and drop a PDF file here, or click to browse
                </p>
                <Button 
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    if (!uploadInProgress) {
                      console.log('[PitchDeckUpload] Button clicked, triggering file input')
                      handleUploadClick()
                    }
                  }} 
                  className="gap-2" 
                  disabled={uploadInProgress}
                >
                  <Upload className="size-4" />
                  Choose PDF File
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                Maximum file size: 20MB • PDF files only
              </p>
            </div>
            {/* Hidden file input always rendered */}
            <input
              ref={fileInputRef}
              type="file"
              onChange={handleFileSelect}
              className="hidden"
              accept=".pdf"
              disabled={uploadInProgress}
            />
          </div>
        </CardContent>
      </Card>
    )
  }

  // If pitch deck exists, show it
  if (hasPitchDeck) {
    const filename = pitchDeckDocument?.filename || 'pitch_deck.pdf'
    const fileSize = pitchDeckDocument?.file_size || 0
    const uploadDate = pitchDeckDocument?.created_at || new Date().toISOString()
    const processingMetadata = deal.enriched_data?.processing_metadata

    return (
      <Card className="border-2 border-primary/20 bg-primary/5">
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="flex size-12 items-center justify-center rounded-lg border border-red-200 bg-red-100 text-red-600">
                  <FileText className="size-6" />
                </div>
                <div>
                  <div className="flex items-center gap-2">
                    <h3 className="text-sm font-semibold">{filename}</h3>
                    <Badge variant="secondary" className="text-xs">
                      Pitch Deck
                    </Badge>
                  </div>
                  <div className="mt-1 flex items-center gap-4 text-xs text-muted-foreground">
                    <span>{formatFileSize(fileSize)}</span>
                    <span>{formatDate(uploadDate)}</span>
                    {processingMetadata?.pitch_type && (
                      <Badge variant="outline" className="text-xs">
                        {processingMetadata.pitch_type}
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handlePreview}
                  className="gap-1"
                >
                  <Eye className="size-4" />
                  Preview
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleDownload}
                  className="gap-1"
                >
                  <Download className="size-4" />
                  Download
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleReplace}
                  className="gap-1"
                >
                  <Upload className="size-4" />
                  Replace
                </Button>
              </div>
            </div>

            {/* Processing status */}
            {processingMetadata && (
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <CheckCircle className="size-3 text-green-600" />
                <span>
                  Processed {processingMetadata.pages_processed || 0} pages • 
                  {processingMetadata.founders_extracted || 0} founders extracted
                </span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  // Default state - no pitch deck uploaded
  return (
    <>
      <Card className="border-2 border-dashed border-primary/20 transition-colors hover:border-primary/50">
        <CardContent className="p-6">
          <div className="space-y-4 text-center">
            <div className="flex justify-center">
              <div className="flex size-16 items-center justify-center rounded-full bg-primary/10">
                <FileText className="size-8 text-primary" />
              </div>
            </div>
            <div>
              <h3 className="mb-2 font-semibold">Upload Pitch Deck</h3>
              <p className="mb-4 text-sm text-muted-foreground">
                This is the main deck used for parsing, enrichment, and scoring.
              </p>
              <Button 
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  if (!uploadInProgress) {
                    console.log('[PitchDeckUpload] Default state button clicked, triggering file input')
                    handleUploadClick()
                  }
                }} 
                className="gap-2" 
                disabled={uploadInProgress}
              >
                <Upload className="size-4" />
                Choose PDF File
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">
              Maximum file size: 20MB • PDF files only
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        onChange={handleFileSelect}
        className="hidden"
        accept=".pdf"
        disabled={uploadInProgress}
      />
    </>
  )
} 