"use client"

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { AlertCircle, FileText } from "lucide-react"
import { DealDetailData } from "@/lib/types/deal-detail"
import { EmptyPlaceholder } from "@/components/empty-placeholder"
import { BackgroundPattern } from "@/components/ui/background-pattern"
import { useExecutiveSummary } from "@/lib/hooks/use-executive-summary"
import { ExecutiveSummarySection } from "./executive-summary-section"

interface AnalystResearchTabProps {
  deal: DealDetailData
}

export function AnalystResearchTab({ deal }: AnalystResearchTabProps) {
  // Use the executive summary hook for data management
  const {
    data: executiveSummaryData,
    loading,
    error,
    refreshing,
    hasData,
    refresh: handleRefresh,
    reload
  } = useExecutiveSummary({ dealId: deal.id })

  if (loading) {
    return (
      <div className="relative min-h-screen">
        <BackgroundPattern variant="dots" opacity={0.02} className="text-gray-400" />
        <div className="relative space-y-8">
          {/* Header Skeleton */}
          <div className="flex items-center justify-between">
            <div>
              <Skeleton className="mb-2 h-8 w-48" />
              <Skeleton className="h-4 w-96" />
            </div>
            <div className="flex gap-3">
              <Skeleton className="h-9 w-20" />
              <Skeleton className="h-9 w-24" />
            </div>
          </div>

          {/* Content Skeleton */}
          <Card className="border-0 bg-white/80 shadow-xl backdrop-blur-md">
            <CardContent className="p-8 space-y-6">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="space-y-3">
                  <Skeleton className="h-6 w-32" />
                  <Skeleton className="h-20 w-full" />
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="space-y-4 text-center">
          <AlertCircle className="mx-auto size-12 text-red-500" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Executive Summary Unavailable</h3>
            <p className="mt-1 text-sm text-gray-600">{error}</p>
          </div>
          <button 
            onClick={reload}
            className="px-4 py-2 text-sm font-medium text-white bg-primary rounded-md hover:bg-primary/90"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  if (!hasData) {
    return (
      <EmptyPlaceholder>
        <EmptyPlaceholder.Icon name="file-text" />
        <EmptyPlaceholder.Title>No Executive Summary Available</EmptyPlaceholder.Title>
        <EmptyPlaceholder.Description>
          The executive summary for this deal has not been generated yet. 
          This typically happens after the pitch deck has been processed and analyzed.
        </EmptyPlaceholder.Description>
      </EmptyPlaceholder>
    )
  }

  return (
    <div className="relative min-h-screen">
      {/* Subtle Background Pattern */}
      <BackgroundPattern variant="dots" opacity={0.02} className="text-gray-400" />

      <div className="relative">
        {/* Executive Summary Section */}
        <ExecutiveSummarySection
          executive_summary={executiveSummaryData?.executive_summary || []}
          metadata={executiveSummaryData?.metadata}
          onRefresh={handleRefresh}
          refreshing={refreshing}
        />
      </div>
    </div>
  )
}


